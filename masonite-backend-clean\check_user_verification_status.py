#!/usr/bin/env python3

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

# Import and initialize Masonite application
from wsgi import application

from app.models.User import User

def check_user_verification():
    email = '<EMAIL>'
    
    try:
        user = User.where('email', email).first()
        
        if user:
            print(f"✅ User found: {email}")
            print(f"📧 Email verified: {user.is_email_verified()}")
            print(f"🕒 Email verified at: {user.email_verified_at}")
            print(f"👤 User details: {user.first_name} {user.last_name}")
            print(f"🆔 User ID: {user.id}")
            
            if user.is_email_verified():
                print("\n⚠️  Email is already verified. To resend verification:")
                print("   1. User needs to be marked as unverified first, OR")
                print("   2. This is the expected behavior for verified users")
            else:
                print("\n📬 Email is not verified - reverification can be sent")
                
        else:
            print(f"❌ User not found: {email}")
            
    except Exception as e:
        print(f"❌ Error checking user: {e}")

if __name__ == "__main__":
    check_user_verification()
