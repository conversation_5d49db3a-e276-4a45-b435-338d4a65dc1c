"""OAuth Service for handling OAuth authentication with multiple providers"""

import os
import requests
import json
from datetime import datetime
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from github import Github
from msal import ConfidentialClientApplication
from app.models.User import User
from app.models.OAuthAuthorizationCode import OAuthAuthorizationCode


def load_env_variable(key, fallback=None):
    """Load environment variable with fallback to .env file"""
    # First try os.getenv
    value = os.getenv(key)
    if value:
        return value
    
    # If not found, try to load from .env file manually
    try:
        env_path = os.path.join(os.path.dirname(__file__), '../../.env')
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        env_key, env_value = line.split('=', 1)
                        if env_key == key:
                            return env_value
    except Exception:
        pass
    
    return fallback


class OAuthService:
    """Service for handling OAuth authentication with Google, GitHub, and Microsoft"""

    def __init__(self):
        # OAuth configuration using improved env loading
        self.google_client_id = load_env_variable('GOOGLE_CLIENT_ID')
        self.google_client_secret = load_env_variable('GOOGLE_CLIENT_SECRET')
        self.github_client_id = load_env_variable('GITHUB_CLIENT_ID')
        self.github_client_secret = load_env_variable('GITHUB_CLIENT_SECRET')
        self.microsoft_client_id = load_env_variable('MICROSOFT_CLIENT_ID')
        self.microsoft_client_secret = load_env_variable('MICROSOFT_CLIENT_SECRET')
        self.oauth_redirect_url = load_env_variable('OAUTH_REDIRECT_URL', 'http://localhost:3002/api/oauth/callback')

    def get_provider_config(self, provider: str):
        """Get OAuth configuration for a specific provider"""
        configs = {
            'google': {
                'client_id': self.google_client_id,
                'client_secret': self.google_client_secret,
                'auth_url': 'https://accounts.google.com/o/oauth2/auth',
                'token_url': 'https://oauth2.googleapis.com/token',
                'scope': 'openid email profile'
            },
            'github': {
                'client_id': self.github_client_id,
                'client_secret': self.github_client_secret,
                'auth_url': 'https://github.com/login/oauth/authorize',
                'token_url': 'https://github.com/login/oauth/access_token',
                'scope': 'user:email'
            },            'microsoft': {
                'client_id': self.microsoft_client_id,
                'client_secret': self.microsoft_client_secret,
                'auth_url': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
                'token_url': 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
                'scope': 'openid email profile User.Read'  # Added openid for proper authorization
            }
        }
        return configs.get(provider)

    def generate_oauth_url(self, provider: str, state: str) -> str:
        """Generate OAuth authorization URL for a provider"""
        config = self.get_provider_config(provider)
        if not config:
            raise ValueError(f"Unsupported provider: {provider}")

        params = {
            'client_id': config['client_id'],
            'redirect_uri': self.oauth_redirect_url,
            'scope': config['scope'],
            'response_type': 'code',
            'state': state,
        }

        # Additional parameters for specific providers
        if provider == 'google':
            params['access_type'] = 'offline'
            params['prompt'] = 'consent'
        elif provider == 'microsoft':
            params['response_mode'] = 'query'

        query_string = '&'.join([f"{k}={requests.utils.quote(str(v))}" for k, v in params.items()])
        return f"{config['auth_url']}?{query_string}"

    def exchange_code_for_token(self, provider: str, code: str) -> str:
        """Exchange authorization code for access token"""
        config = self.get_provider_config(provider)
        if not config:
            raise ValueError(f"Unsupported provider: {provider}")

        data = {
            'client_id': config['client_id'],
            'client_secret': config['client_secret'],
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': self.oauth_redirect_url,
        }

        headers = {'Accept': 'application/json'}
        
        response = requests.post(config['token_url'], data=data, headers=headers)
        response.raise_for_status()
        
        token_data = response.json()
          # Return appropriate token based on provider
        if provider == 'google':
            return token_data.get('id_token') or token_data.get('access_token')
        else:
            return token_data.get('access_token')

    def verify_google_token(self, token: str) -> dict:
        """Verify Google ID token and extract user info"""
        try:
            # Add clock tolerance for time sync issues
            import time
            current_time = int(time.time())
            
            # Verify the token with clock skew tolerance
            id_info = id_token.verify_oauth2_token(
                token, 
                google_requests.Request(), 
                self.google_client_id,
                clock_skew_in_seconds=60  # Allow 60 seconds clock skew
            )

            # Extract user information
            first_name = id_info.get('given_name', '')
            last_name = id_info.get('family_name', '')
            full_name = id_info.get('name', '')
            
            # If given_name/family_name not available, try to split the full name
            if not first_name and full_name:
                name_parts = full_name.split(' ', 1)
                first_name = name_parts[0]
                last_name = name_parts[1] if len(name_parts) > 1 else ''

            return {
                'id': id_info['sub'],
                'email': id_info['email'],
                'firstName': first_name,
                'lastName': last_name,
                'avatarUrl': id_info.get('picture'),
                'emailVerified': id_info.get('email_verified', False),
                'provider': 'google'
            }
        except ValueError as e:
            error_msg = str(e)
            if "Token used too early" in error_msg:
                # Extract details and provide helpful error
                print(f"❌ Google token time sync error: {error_msg}")
                raise ValueError(f"Invalid Google token: {error_msg}. Check that your computer's clock is set correctly.")
            raise ValueError(f"Invalid Google token: {error_msg}")

    def verify_github_token(self, access_token: str) -> dict:
        """Verify GitHub access token and extract user info"""
        try:
            # Get user info from GitHub API
            headers = {
                'Authorization': f'token {access_token}',
                'User-Agent': 'MasoniteSecureApp',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            # Test token validity first
            user_response = requests.get('https://api.github.com/user', headers=headers)
            if user_response.status_code == 401:
                raise ValueError(f"Invalid GitHub token: 401 Client Error: Unauthorized for url: https://api.github.com/user")
            user_response.raise_for_status()
            user_data = user_response.json()
            
            # Get user email (might be private)
            email_response = requests.get('https://api.github.com/user/emails', headers=headers)
            if email_response.status_code == 200:
                emails = email_response.json()
                primary_email = next((email for email in emails if email.get('primary')), None)
            else:
                primary_email = None
            
            # Parse name
            full_name = user_data.get('name', '')
            name_parts = full_name.split(' ', 1) if full_name else ['', '']
            first_name = name_parts[0] or user_data.get('login', '')
            last_name = name_parts[1] if len(name_parts) > 1 else ''

            return {
                'id': str(user_data['id']),
                'email': primary_email.get('email') if primary_email else user_data.get('email'),
                'firstName': first_name,
                'lastName': last_name,
                'avatarUrl': user_data.get('avatar_url'),
                'emailVerified': primary_email.get('verified', False) if primary_email else False,
                'provider': 'github'
            }
        except requests.RequestException as e:
            raise ValueError(f"Invalid GitHub token: {str(e)}")

    def verify_microsoft_token(self, access_token: str) -> dict:
        """Verify Microsoft access token and extract user info"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get('https://graph.microsoft.com/v1.0/me', headers=headers)
            if response.status_code == 403:
                # Provide more specific error for permission issues
                raise ValueError(f"Invalid Microsoft token: 403 Client Error: Forbidden for url: https://graph.microsoft.com/v1.0/me. Check that your app has the correct permissions (User.Read).")
            response.raise_for_status()
            user_data = response.json()
            
            return {
                'id': user_data['id'],
                'email': user_data.get('mail') or user_data.get('userPrincipalName'),
                'firstName': user_data.get('givenName', ''),
                'lastName': user_data.get('surname', ''),
                'avatarUrl': None,  # Would need additional API call
                'emailVerified': True,  # Microsoft emails are typically verified
                'provider': 'microsoft'
            }
        except requests.RequestException as e:
            raise ValueError(f"Invalid Microsoft token: {str(e)}")

    def find_or_create_oauth_user(self, provider: str, oauth_data: dict) -> User:
        """Find existing user or create new user from OAuth data with proper data merging"""
        email = oauth_data['email']
        print(f"🔍 OAuth login attempt for: {email} via {provider}")

        # Try to find existing user by email (including soft-deleted)
        existing_user = User.where('email', email).first()

        if existing_user:
            print(f"✅ Found existing active user: {email}")
            # CRITICAL FIX: Update OAuth provider info and merge data properly
            current_providers = existing_user.oauth_providers or []
            if provider not in current_providers:
                current_providers.append(provider)
                existing_user.oauth_providers = current_providers

            # Update avatar if OAuth provides one and user doesn't have one
            if oauth_data.get('avatarUrl') and not existing_user.avatar_url:
                existing_user.avatar_url = oauth_data.get('avatarUrl')

            # Ensure email is verified if OAuth provider verified it
            if oauth_data.get('emailVerified') and not existing_user.email_verified_at:
                existing_user.email_verified_at = datetime.now()

            existing_user.save()
            print(f"✅ Updated existing user {email} with OAuth provider {provider}")
            return existing_user

        # If no active user found, check for soft-deleted user with preserved data
        soft_deleted_user = User.with_trashed().where('email', email).first()
        if soft_deleted_user and soft_deleted_user.deleted_at is not None:
            print(f"🔄 Found soft-deleted user {email}, checking for preserved data")

            # Check if this user has preserved data
            try:
                from app.services.AccountDeletionService import AccountDeletionService
                deletion_service = AccountDeletionService()
                preserved_data_info = deletion_service.check_preserved_data(email)

                if preserved_data_info.get('hasPreservedData'):
                    print(f"✅ Restoring user {email} via OAuth with preserved data")

                    # Restore the user by removing soft delete timestamp
                    soft_deleted_user.deleted_at = None
                    soft_deleted_user.is_active = True

                    # Update OAuth provider info
                    current_providers = soft_deleted_user.oauth_providers or []
                    if provider not in current_providers:
                        current_providers.append(provider)
                        soft_deleted_user.oauth_providers = current_providers

                    # Update with OAuth data
                    if oauth_data.get('avatarUrl') and not soft_deleted_user.avatar_url:
                        soft_deleted_user.avatar_url = oauth_data.get('avatarUrl')
                    if oauth_data.get('emailVerified'):
                        soft_deleted_user.email_verified_at = datetime.now()

                    soft_deleted_user.save()

                    # Trigger data restoration with default options (user can choose later)
                    deletion_service.restore_user_data(str(soft_deleted_user.id))

                    print(f"✅ User {email} restored successfully via OAuth")
                    return soft_deleted_user
                else:
                    print(f"⚠️ Soft-deleted user {email} found but no preserved data available")
                    # Fall through to create new user
            except Exception as restore_error:
                print(f"❌ Error during OAuth user restoration: {restore_error}")
                # Fall through to create new user

        # CRITICAL FIX: Create new user but check for preserved data first
        print(f"🆕 Creating new user for {email} via OAuth {provider}")

        # Check if there's preserved data for this email (from previous account deletion)
        preserved_data_info = None
        try:
            from app.services.AccountDeletionService import AccountDeletionService
            deletion_service = AccountDeletionService()
            preserved_data_info = deletion_service.check_preserved_data(email)

            if preserved_data_info.get('hasPreservedData'):
                print(f"🔄 Found preserved data for new OAuth user {email}")
        except Exception as e:
            print(f"⚠️ Error checking preserved data for new OAuth user: {e}")

        # Create new user with proper JSON field handling
        full_name = f"{oauth_data['firstName']} {oauth_data['lastName']}".strip()
        user_data = {
            'name': full_name or oauth_data['firstName'] or 'OAuth User',  # Add required name field
            'email': email,
            'password': 'oauth_user_no_password',  # Dummy password for OAuth users
            'first_name': oauth_data['firstName'],
            'last_name': oauth_data['lastName'],
            'email_verified_at': datetime.now() if oauth_data.get('emailVerified') else None,
            'avatar_url': oauth_data.get('avatarUrl'),
            'oauth_providers': [provider],  # Store as list, model will handle JSON conversion
            'roles': ['user'],  # Store as list, model will handle JSON conversion
            'is_active': True
        }

        new_user = User.create(user_data)
        print(f"✅ Created new OAuth user: {email}")

        # If there was preserved data, restore it now
        try:
            if preserved_data_info and preserved_data_info.get('hasPreservedData'):
                print(f"🔄 Restoring preserved data for new OAuth user {email}")
                deletion_service.restore_user_data(str(new_user.id), email)
                print(f"✅ Preserved data restored for OAuth user {email}")
        except Exception as restore_error:
            print(f"❌ Error restoring preserved data for OAuth user: {restore_error}")

        return new_user

    def is_provider_configured(self, provider: str) -> bool:
        """Check if OAuth provider is properly configured"""
        config = self.get_provider_config(provider)
        if not config:
            return False
        
        return bool(config['client_id'] and config['client_secret'])

    def get_available_providers(self) -> list:
        """Get list of available and configured OAuth providers"""
        providers = []
        
        for provider_name in ['google', 'github', 'microsoft']:
            providers.append({
                'name': provider_name,
                'enabled': True,
                'configured': self.is_provider_configured(provider_name)
            })
        
        return providers
