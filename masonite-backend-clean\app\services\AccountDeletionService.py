"""Account Deletion Service for GDPR-compliant account deletion"""

from datetime import datetime, timedelta
from app.models.User import User
from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.models.Payment import Payment
import json
import secrets


class AccountDeletionService:
    """Service for handling account deletion requests and data preservation"""

    def __init__(self):
        """Initialize the AccountDeletionService"""
        # No dependencies needed - using BrevoEmailService pattern like other controllers
        pass

    def request_account_deletion(self, user_id, preferences):
        """Request account deletion with data preservation preferences"""
        try:
            # Get user details
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Check if there's already a pending deletion request
            existing_request = AccountDeletionRecord.find_by_email(user.email)
            if existing_request and existing_request.deletion_status == 'pending_confirmation':
                return {
                    'message': 'Account deletion request already pending. Please check your email for confirmation.',
                    'deletionId': existing_request.deletion_id,
                    'confirmationRequired': True
                }

            # Create deletion request
            deletion_record = AccountDeletionRecord.create_deletion_request(
                user_id=user_id,
                email=user.email,
                preferences=preferences
            )

            # Send confirmation email
            self._send_deletion_confirmation_email(user, deletion_record)

            return {
                'message': 'Account deletion request created. Please check your email to confirm.',
                'deletionId': deletion_record.deletion_id,
                'confirmationRequired': True,
                'confirmationToken': deletion_record.confirmation_token
            }

        except Exception as e:
            print(f"❌ Account deletion request error: {str(e)}")
            raise Exception(f"Failed to request account deletion: {str(e)}")

    def confirm_account_deletion(self, token):
        """Confirm account deletion with token"""
        try:
            # Find deletion record by token
            deletion_record = AccountDeletionRecord.find_by_token(token)
            if not deletion_record:
                raise Exception("Invalid or expired confirmation token")

            # Check if token is still valid
            if not deletion_record.is_confirmation_token_valid():
                raise Exception("Confirmation token has expired")

            # Find the user
            user = User.where('email', deletion_record.email).first()
            if not user:
                # User might already be deleted, proceed with record cleanup
                deletion_record.complete_deletion()
                return {
                    'message': 'Account deletion completed',
                    'deletionId': deletion_record.deletion_id
                }

            # CRITICAL FIX: Clear any existing preserved data for this email first
            # This ensures that if user chooses NOT to preserve data this time,
            # old preserved data from previous deletions is removed
            self._clear_existing_preserved_data(deletion_record.email)

            # Check if user wants to preserve ANY data
            wants_to_preserve_data = (
                getattr(deletion_record, 'preserve_payment_data', False) or
                getattr(deletion_record, 'preserve_transaction_history', False) or
                getattr(deletion_record, 'preserve_profile_data', False) or
                getattr(deletion_record, 'preserve_security_logs', False)
            )

            # SOPHISTICATED PRESERVATION LOGIC: Only preserve data that user explicitly chose
            # Each category is handled independently based on user's specific preferences

            # Preserve profile data ONLY if user explicitly chose to preserve it
            if getattr(deletion_record, 'preserve_profile_data', False):
                profile_data = self._preserve_profile_data(user)
                if profile_data:
                    deletion_record.preserved_user_data = json.dumps(profile_data)
                    print(f"✅ Profile data preserved for {user.email}")
                    print(f"📊 Profile data content: {json.dumps(profile_data, indent=2)}")
                else:
                    deletion_record.preserved_user_data = None
                    print(f"⚠️ Profile data preservation requested but no data found for {user.email}")
            else:
                deletion_record.preserved_user_data = None
                print(f"🚫 Profile data NOT preserved (user choice) for {user.email}")

            # Preserve payment data ONLY if user explicitly chose to preserve it
            if getattr(deletion_record, 'preserve_payment_data', False):
                payment_data = self._preserve_payment_data(user)
                if payment_data:
                    deletion_record.preserved_payment_data = json.dumps(payment_data)
                    print(f"✅ Payment data preserved for {user.email}")
                    print(f"💳 Payment data content: {json.dumps(payment_data, indent=2)}")
                else:
                    deletion_record.preserved_payment_data = None
                    print(f"⚠️ Payment data preservation requested but no data found for {user.email}")
            else:
                deletion_record.preserved_payment_data = None
                print(f"🚫 Payment data NOT preserved (user choice) for {user.email}")

            # Preserve transaction history ONLY if user explicitly chose to preserve it
            if getattr(deletion_record, 'preserve_transaction_history', False):
                transaction_data = self._preserve_transaction_data(user)
                if transaction_data:
                    deletion_record.preserved_transaction_data = json.dumps(transaction_data)
                    print(f"✅ Transaction history preserved for {user.email}")
                    print(f"📊 Transaction data content: {json.dumps(transaction_data, indent=2)}")
                else:
                    deletion_record.preserved_transaction_data = None
                    print(f"⚠️ Transaction history preservation requested but no data found for {user.email}")
            else:
                deletion_record.preserved_transaction_data = None
                print(f"🚫 Transaction history NOT preserved (user choice) for {user.email}")

            # Preserve security logs ONLY if user explicitly chose to preserve it
            if getattr(deletion_record, 'preserve_security_logs', False):
                security_data = self._preserve_security_data(user)
                if security_data:
                    deletion_record.preserved_security_data = json.dumps(security_data)
                    print(f"✅ Security data preserved for {user.email}")
                    print(f"🔒 Security data content: {json.dumps(security_data, indent=2)}")
                else:
                    deletion_record.preserved_security_data = None
                    print(f"⚠️ Security data preservation requested but no data found for {user.email}")
            else:
                deletion_record.preserved_security_data = None
                print(f"🚫 Security data NOT preserved (user choice) for {user.email}")

            # CRITICAL FIX: Delete data that user chose NOT to preserve BEFORE deleting user account
            self._delete_non_preserved_data(user, deletion_record)

            # Confirm deletion
            deletion_record.confirm_deletion()

            # Actually delete the user account
            self._delete_user_account(user)

            # Complete the deletion
            deletion_record.complete_deletion()

            # Build preserved data summary from deletion record
            preserved_summary = {}
            if deletion_record.preserved_user_data:
                preserved_summary['profile'] = 'Preserved'
            if deletion_record.preserved_payment_data:
                preserved_summary['payments'] = 'Preserved'
            if deletion_record.preserved_transaction_data:
                preserved_summary['transactions'] = 'Preserved'
            if deletion_record.preserved_security_data:
                preserved_summary['security'] = 'Preserved'

            return {
                'message': 'Account deletion completed successfully',
                'deletionId': deletion_record.deletion_id,
                'preservedDataSummary': preserved_summary
            }

        except Exception as e:
            print(f"❌ Account deletion confirmation error: {str(e)}")
            raise Exception(f"Failed to confirm account deletion: {str(e)}")

    def export_user_data(self, user_id):
        """Export all user data for download"""
        try:
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Collect all user data
            user_data = {
                'profile': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'phone': user.phone,
                    'created_at': str(user.created_at),
                    'updated_at': str(user.updated_at),
                    'last_login_at': str(user.last_login_at) if user.last_login_at else None,
                    'email_verified': user.email_verified_at is not None,
                    'two_factor_enabled': user.two_factor_enabled
                },
                'payments': [],
                'security_logs': {
                    'login_attempts': user.login_attempts,
                    'locked_until': str(user.locked_until) if user.locked_until else None,
                    'oauth_providers': user.oauth_providers
                }
            }

            # Get payment data if exists
            try:
                payments = Payment.where('user_id', user_id).get()
                for payment in payments:
                    user_data['payments'].append({
                        'id': payment.id,
                        'amount': payment.amount,
                        'currency': payment.currency,
                        'status': payment.status,
                        'created_at': str(payment.created_at)
                    })
            except:
                pass  # Payment table might not exist

            return user_data

        except Exception as e:
            print(f"❌ Data export error: {str(e)}")
            raise Exception(f"Failed to export user data: {str(e)}")

    def request_data_export(self, user_id):
        """Request data export via email"""
        try:
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Export data
            user_data = self.export_user_data(user_id)

            # Send email with data (in production, this would be a secure download link)
            self._send_data_export_email(user, user_data)

            return {
                'message': 'Data export has been sent to your email address'
            }

        except Exception as e:
            print(f"❌ Data export request error: {str(e)}")
            raise Exception(f"Failed to request data export: {str(e)}")

    def check_preserved_data(self, email):
        """Check if user has preserved data for restoration"""
        try:
            # CRITICAL FIX: Order by completed_at DESC to get the most recent deletion record
            # This ensures we check the latest deletion, not an older one
            deletion_record = AccountDeletionRecord.where('email', email)\
                .where('deletion_status', 'completed')\
                .where('expires_at', '>', datetime.now())\
                .order_by('completed_at', 'desc')\
                .first()

            if not deletion_record:
                return {
                    'hasPreservedData': False
                }

            preserved_summary = {}

            # CRITICAL FIX: Check if data is actually preserved and meaningful
            # Don't just check if field exists, check if it contains actual data

            # Check profile data
            if deletion_record.preserved_user_data:
                try:
                    profile_data = deletion_record.preserved_user_data
                    if isinstance(profile_data, str):
                        profile_data = json.loads(profile_data)
                    # Only count as preserved if it has meaningful data
                    if profile_data and isinstance(profile_data, dict) and any(profile_data.values()):
                        preserved_summary['profile'] = True
                except:
                    pass  # Invalid data, don't count as preserved

            # Check payment data
            if deletion_record.preserved_payment_data:
                try:
                    payment_data = deletion_record.preserved_payment_data
                    if isinstance(payment_data, str):
                        payment_data = json.loads(payment_data)
                    # Count as preserved if it has any data (including placeholders)
                    # Placeholders indicate user requested preservation of this category
                    if payment_data and isinstance(payment_data, list) and len(payment_data) > 0:
                        preserved_summary['payments'] = True
                except:
                    pass  # Invalid data, don't count as preserved

            # Check transaction data
            if deletion_record.preserved_transaction_data:
                try:
                    transaction_data = deletion_record.preserved_transaction_data
                    if isinstance(transaction_data, str):
                        transaction_data = json.loads(transaction_data)
                    # Count as preserved if it has any data (including placeholders)
                    # Placeholders indicate user requested preservation of this category
                    if transaction_data and isinstance(transaction_data, list) and len(transaction_data) > 0:
                        preserved_summary['transactions'] = True
                except:
                    pass  # Invalid data, don't count as preserved

            # Check security data
            if deletion_record.preserved_security_data:
                try:
                    security_data = deletion_record.preserved_security_data
                    if isinstance(security_data, str):
                        security_data = json.loads(security_data)
                    # Only count as preserved if it has meaningful data
                    # For security data, consider False as meaningful for boolean fields
                    if security_data and isinstance(security_data, dict):
                        # Check if any value is not None (False is meaningful for booleans)
                        has_meaningful_data = any(
                            value is not None for value in security_data.values()
                        )
                        if has_meaningful_data:
                            preserved_summary['security'] = True
                except:
                    pass  # Invalid data, don't count as preserved

            # CRITICAL FIX: Only return hasPreservedData=true if there's actually preserved data
            # If the preserved_summary is empty, it means no data was preserved
            has_preserved_data = len(preserved_summary) > 0

            return {
                'hasPreservedData': has_preserved_data,
                'deletionRecord': {
                    'deletion_id': deletion_record.deletion_id,
                    'completed_at': str(deletion_record.completed_at),
                    'expires_at': str(deletion_record.expires_at)
                },
                'preservedDataSummary': preserved_summary
            }

        except Exception as e:
            print(f"❌ Check preserved data error: {str(e)}")
            return {'hasPreservedData': False}

    def restore_user_data(self, user_id, email=None, restore_options=None):
        """Restore preserved data during signup - overloaded method"""
        try:
            # If called with just user_id, find user and email
            if email is None:
                user = User.find(user_id)
                if not user:
                    raise Exception("User not found")
                email = user.email
            
            # Default restore options - DON'T restore anything automatically
            # Only restore data if explicitly requested by the user
            if restore_options is None:
                restore_options = {
                    'restoreProfileData': False,  # Don't restore profile data automatically
                    'restorePaymentData': False,  # Don't restore payment data automatically
                    'restoreTransactionHistory': False,  # Don't restore transaction history automatically
                    'restoreSecurityLogs': False  # Don't restore security logs automatically
                }
            
            # Find deletion record with preserved data - get the most recent one
            deletion_record = AccountDeletionRecord.where('email', email)\
                .where('deletion_status', 'completed')\
                .where('expires_at', '>', datetime.now())\
                .order_by('completed_at', 'desc')\
                .first()

            if not deletion_record:
                print(f"⚠️ No preserved data found for email: {email}")
                return {
                    'message': 'No preserved data found to restore',
                    'restoredData': {}
                }

            # Get the user if not already loaded
            if 'user' not in locals():
                user = User.find(user_id)
                if not user:
                    raise Exception("User not found")

            restored_data = {}

            # Restore profile data
            if restore_options.get('restoreProfileData') and deletion_record.preserved_user_data:
                profile_data = deletion_record.preserved_user_data
                if isinstance(profile_data, str):
                    profile_data = json.loads(profile_data)

                # Only update fields that were preserved and are not already set by new signup
                if profile_data.get('phone') and not user.phone:
                    user.phone = profile_data.get('phone')

                # Preserve any additional data but don't overwrite new signup data
                user.save()

                restored_data['profile'] = True
                print(f"✅ Profile data restored for user: {user.email}")

            # Restore payment data (if requested and available)
            if restore_options.get('restorePaymentData') and deletion_record.preserved_payment_data:
                payment_data = deletion_record.preserved_payment_data
                if isinstance(payment_data, str):
                    payment_data = json.loads(payment_data)

                # Note: In a real implementation, you'd restore payment records to the Payment table
                # For now, we'll just mark it as restored
                restored_data['payments'] = len(payment_data) if isinstance(payment_data, list) else 1
                print(f"✅ Payment data restored for user: {user.email}")

            # Restore security logs (if requested and available)
            if restore_options.get('restoreSecurityLogs') and deletion_record.preserved_security_data:
                security_data = deletion_record.preserved_security_data
                if isinstance(security_data, str):
                    security_data = json.loads(security_data)

                # Restore security-related fields
                if security_data.get('two_factor_enabled') and not user.two_factor_enabled:
                    # Note: Don't automatically re-enable 2FA, just note it was preserved
                    pass

                restored_data['security'] = True
                print(f"✅ Security data restored for user: {user.email}")

            # Mark the data as restored but don't delete it yet (in case user wants to restore more)
            deletion_record.data_restored_at = datetime.now()
            deletion_record.save()

            print(f"✅ Data restoration completed for user: {email}")
            return {
                'message': 'Data restoration completed successfully',
                'restoredData': restored_data
            }

        except Exception as e:
            print(f"❌ Data restoration error: {str(e)}")
            return {
                'message': f'Failed to restore user data: {str(e)}',
                'restoredData': {}
            }

    def restore_user_data_with_options(self, user_id, email, restore_options):
        """Original restore method with full options"""
        return self.restore_user_data(user_id, email, restore_options)

    def delete_preserved_data(self, email):
        """Permanently delete all preserved data"""
        try:
            deletion_records = AccountDeletionRecord.where('email', email).get()
            
            for record in deletion_records:
                record.delete()

            return {
                'message': f'All preserved data for {email} has been permanently deleted'
            }

        except Exception as e:
            print(f"❌ Delete preserved data error: {str(e)}")
            raise Exception(f"Failed to delete preserved data: {str(e)}")

    def cleanup_expired_deletion_records(self):
        """Clean up expired deletion records"""
        try:
            count = AccountDeletionRecord.cleanup_expired()
            return {
                'message': f'Cleaned up {count} expired deletion records',
                'cleanedCount': count
            }

        except Exception as e:
            print(f"❌ Cleanup error: {str(e)}")
            raise Exception(f"Failed to cleanup expired records: {str(e)}")

    def _preserve_profile_data(self, user):
        """Preserve ONLY basic profile data - NO payment, security, or transaction data"""
        try:
            profile_data = {
                'first_name': getattr(user, 'first_name', None),
                'last_name': getattr(user, 'last_name', None),
                'phone': getattr(user, 'phone', None),
                'name': getattr(user, 'name', None),
                'avatar_url': getattr(user, 'avatar_url', None),
                'created_at': str(user.created_at) if hasattr(user, 'created_at') and user.created_at else None,
                'updated_at': str(user.updated_at) if hasattr(user, 'updated_at') and user.updated_at else None,
                'is_active': getattr(user, 'is_active', None),
                'roles': getattr(user, 'roles', None)
            }
            # Only return data if there's meaningful content
            if any(value is not None for value in profile_data.values()):
                return profile_data
            return None
        except Exception as e:
            print(f"❌ Error preserving profile data: {str(e)}")
            return None

    def _preserve_payment_data(self, user):
        """Preserve ONLY payment records from Payment table - NO user profile data"""
        try:
            # Get actual payment records from Payment table
            payments = Payment.where('user_id', user.id).get()
            payment_list = []

            for payment in payments:
                payment_record = {
                    'id': getattr(payment, 'id', None),
                    'amount': getattr(payment, 'amount', None),
                    'currency': getattr(payment, 'currency', None),
                    'status': getattr(payment, 'status', None),
                    'payment_method': getattr(payment, 'payment_method', None),
                    'razorpay_order_id': getattr(payment, 'razorpay_order_id', None),
                    'razorpay_payment_id': getattr(payment, 'razorpay_payment_id', None),
                    'description': getattr(payment, 'description', None),
                    'created_at': str(payment.created_at) if hasattr(payment, 'created_at') and payment.created_at else None,
                    'updated_at': str(payment.updated_at) if hasattr(payment, 'updated_at') and payment.updated_at else None
                }
                payment_list.append(payment_record)

            # Only return actual payment data if found
            if payment_list:
                print(f"💳 Found {len(payment_list)} payment records to preserve")
                return payment_list
            else:
                print(f"💳 No payment records found in Payment table")
                return None

        except Exception as e:
            print(f"❌ Error preserving payment data: {str(e)}")
            return None

    def _preserve_transaction_data(self, user):
        """Preserve ONLY transaction history - NO profile or payment data"""
        try:
            # Get transaction data from PaymentRefund table (which contains transaction history)
            transaction_list = []
            try:
                from app.models.PaymentRefund import PaymentRefund
                refunds = PaymentRefund.where('user_id', user.id).get()
                for refund in refunds:
                    transaction_list.append({
                        'id': getattr(refund, 'id', None),
                        'payment_id': getattr(refund, 'payment_id', None),
                        'amount': getattr(refund, 'amount', None),
                        'reason': getattr(refund, 'reason', None),
                        'status': getattr(refund, 'status', None),
                        'created_at': str(refund.created_at) if hasattr(refund, 'created_at') and refund.created_at else None
                    })
            except Exception as e:
                print(f"⚠️ Could not load PaymentRefund model: {e}")

            # Also check for any other transaction-related data
            # In a real system, this would query Transaction, Order, Invoice tables etc.

            if transaction_list:
                print(f"📊 Found {len(transaction_list)} transaction records to preserve")
                return transaction_list
            else:
                print(f"📊 No transaction records found")
                return None

        except Exception as e:
            print(f"❌ Error preserving transaction data: {str(e)}")
            return None

    def _preserve_security_data(self, user):
        """Preserve ONLY security-related data - NO profile or payment data"""
        try:
            # Get security events from SecurityEvent table
            security_events = []
            try:
                from app.models.SecurityEvent import SecurityEvent
                events = SecurityEvent.where('user_id', user.id).get()
                for event in events:
                    security_events.append({
                        'id': getattr(event, 'id', None),
                        'event_type': getattr(event, 'event_type', None),
                        'ip_address': getattr(event, 'ip_address', None),
                        'user_agent': getattr(event, 'user_agent', None),
                        'created_at': str(event.created_at) if hasattr(event, 'created_at') and event.created_at else None
                    })
            except Exception as e:
                print(f"⚠️ Could not load SecurityEvent model: {e}")

            # Security-related fields from User table only
            security_data = {
                'last_login_at': str(user.last_login_at) if hasattr(user, 'last_login_at') and user.last_login_at else None,
                'last_login_ip': getattr(user, 'last_login_ip', None),
                'last_login_user_agent': getattr(user, 'last_login_user_agent', None),
                'oauth_providers': getattr(user, 'oauth_providers', None),
                'two_factor_enabled': getattr(user, 'two_factor_enabled', False),
                'login_attempts': getattr(user, 'login_attempts', None),
                'last_failed_login': str(user.last_failed_login) if hasattr(user, 'last_failed_login') and user.last_failed_login else None,
                'password_changed_at': str(user.password_changed_at) if hasattr(user, 'password_changed_at') and user.password_changed_at else None,
                'security_events': security_events
            }

            # Only return data if there's meaningful content
            if any(value is not None and value != [] for value in security_data.values()):
                print(f"🔒 Found security data: {len(security_events)} events, login info, 2FA status")
                return security_data
            return None
        except Exception as e:
            print(f"❌ Error preserving security data: {str(e)}")
            return None

    def _delete_non_preserved_data(self, user, deletion_record):
        """Delete data that user chose NOT to preserve"""
        try:
            print(f"🗑️ Deleting non-preserved data for user: {user.email}")

            # Delete payment data if NOT preserved
            if not getattr(deletion_record, 'preserve_payment_data', False):
                try:
                    payments = Payment.where('user_id', user.id).get()
                    payment_count = len(payments)
                    for payment in payments:
                        payment.delete()
                    print(f"🗑️ Deleted {payment_count} payment records (not preserved)")
                except Exception as e:
                    print(f"⚠️ Error deleting payment data: {e}")

            # Delete transaction data if NOT preserved
            if not getattr(deletion_record, 'preserve_transaction_history', False):
                try:
                    from app.models.PaymentRefund import PaymentRefund
                    refunds = PaymentRefund.where('user_id', user.id).get()
                    refund_count = len(refunds)
                    for refund in refunds:
                        refund.delete()
                    print(f"🗑️ Deleted {refund_count} transaction records (not preserved)")
                except Exception as e:
                    print(f"⚠️ Error deleting transaction data: {e}")

            # Delete security data if NOT preserved
            if not getattr(deletion_record, 'preserve_security_logs', False):
                try:
                    from app.models.SecurityEvent import SecurityEvent
                    events = SecurityEvent.where('user_id', str(user.id)).get()
                    event_count = len(events)
                    for event in events:
                        event.delete()
                    print(f"🗑️ Deleted {event_count} security events (not preserved)")
                except Exception as e:
                    print(f"⚠️ Error deleting security events: {e}")

            print(f"✅ Non-preserved data deletion completed for user: {user.email}")

        except Exception as e:
            print(f"❌ Error deleting non-preserved data: {str(e)}")
            # Don't fail the entire deletion process if this fails

    def _delete_user_account(self, user):
        """Actually delete the user account and related data"""
        try:
            # Clear sensitive fields before deletion (skip password as it has not-null constraint)
            if hasattr(user, 'api_token'):
                user.api_token = None
            if hasattr(user, 'two_factor_secret'):
                user.two_factor_secret = None
            if hasattr(user, 'recovery_codes'):
                user.recovery_codes = None

            # Save the changes before deletion
            user.save()

            # Delete the user (this will remove all data including password)
            user.delete()

        except Exception as e:
            print(f"❌ User account deletion error: {str(e)}")
            raise e

    def _send_deletion_confirmation_email(self, user, deletion_record):
        """Send deletion confirmation email"""
        try:
            print(f"📧 Sending deletion confirmation email to {user.email}")
            print(f"🔗 Confirmation token: {deletion_record.confirmation_token}")
            
            # Get preferences from deletion record
            preferences = {}
            try:
                if hasattr(deletion_record, 'preserve_payment_data'):
                    preferences['preservePaymentData'] = getattr(deletion_record, 'preserve_payment_data', False)
                if hasattr(deletion_record, 'preserve_transaction_history'):
                    preferences['preserveTransactionHistory'] = getattr(deletion_record, 'preserve_transaction_history', False)
                if hasattr(deletion_record, 'preserve_profile_data'):
                    preferences['preserveProfileData'] = getattr(deletion_record, 'preserve_profile_data', False)
                if hasattr(deletion_record, 'preserve_security_logs'):
                    preferences['preserveSecurityLogs'] = getattr(deletion_record, 'preserve_security_logs', False)
                if hasattr(deletion_record, 'custom_retention_period'):
                    preferences['customRetentionPeriod'] = getattr(deletion_record, 'custom_retention_period', 30)
                if hasattr(deletion_record, 'deletion_reason'):
                    preferences['reason'] = getattr(deletion_record, 'deletion_reason', None)
            except Exception as e:
                print(f"⚠️ Error getting preferences: {str(e)}")
                preferences = {
                    'preservePaymentData': False,
                    'preserveTransactionHistory': False,
                    'preserveProfileData': False,
                    'preserveSecurityLogs': False,
                    'customRetentionPeriod': 30
                }
            
            email_sent = False
            email_method = "none"
            
            # Try Brevo API first (more reliable)
            try:
                from app.services.BrevoEmailService import BrevoEmailService
                brevo_service = BrevoEmailService()
                api_result = brevo_service.send_account_deletion_confirmation_email(user, deletion_record, preferences)
                
                if api_result['success']:
                    email_sent = True
                    email_method = "brevo_api"
                    print(f"✅ Account deletion confirmation email sent via Brevo API to {user.email} (Message ID: {api_result.get('message_id')})")
                else:
                    print(f"⚠️  Brevo API failed: {api_result['error']}")
                    
            except Exception as api_error:
                print(f"⚠️  Brevo API service error: {api_error}")
            
            # Fallback to SMTP if API failed
            if not email_sent:
                try:
                    from masonite.facades import Mail
                    from app.mailables.AccountDeletionConfirmation import AccountDeletionConfirmation
                    # Send email using SMTP
                    Mail.mailable(AccountDeletionConfirmation(user, deletion_record, preferences).to(user.email)).send()
                    email_sent = True
                    email_method = "smtp"
                    print(f"✅ Account deletion confirmation email sent via SMTP to {user.email}")
                except Exception as smtp_error:
                    print(f"❌ SMTP also failed: {smtp_error}")
                    email_method = "failed"
            
            # Log email sending result
            if not email_sent:
                print(f"❌ Failed to send account deletion confirmation email via both API and SMTP")
            else:
                print(f"📧 Account deletion confirmation email successfully sent via {email_method}")
            
        except Exception as e:
            print(f"❌ Email sending error: {str(e)}")

    def _send_data_export_email(self, user, user_data):
        """Send data export email"""
        try:
            print(f"📧 Sending data export email to {user.email}")
            print(f"📊 Data size: {len(str(user_data))} characters")
            
            email_sent = False
            email_method = "none"
            
            # Try Brevo API first (more reliable)
            try:
                from app.services.BrevoEmailService import BrevoEmailService
                brevo_service = BrevoEmailService()
                api_result = brevo_service.send_account_deletion_completed_email(user, user_data)
                
                if api_result['success']:
                    email_sent = True
                    email_method = "brevo_api"
                    print(f"✅ Data export email sent via Brevo API to {user.email} (Message ID: {api_result.get('message_id')})")
                else:
                    print(f"⚠️  Brevo API failed: {api_result['error']}")
                    
            except Exception as api_error:
                print(f"⚠️  Brevo API service error: {api_error}")
            
            # Fallback to SMTP if API failed
            if not email_sent:
                try:
                    from masonite.facades import Mail
                    from app.mailables.AccountDeletionCompleted import AccountDeletionCompleted
                    # Send email using SMTP
                    Mail.mailable(AccountDeletionCompleted(user, user_data).to(user.email)).send()
                    email_sent = True
                    email_method = "smtp"
                    print(f"✅ Data export email sent via SMTP to {user.email}")
                except Exception as smtp_error:
                    print(f"❌ SMTP also failed: {smtp_error}")
                    email_method = "failed"
            
            # Log email sending result
            if not email_sent:
                print(f"❌ Failed to send data export email via both API and SMTP")
            else:
                print(f"📧 Data export email successfully sent via {email_method}")
            
        except Exception as e:
            print(f"❌ Email sending error: {str(e)}")

    def _get_preserved_data_summary(self, preserved_data):
        """Get summary of preserved data"""
        summary = {}

        if preserved_data.get('user_data'):
            summary['profile'] = 'Preserved'
        if preserved_data.get('payment_data'):
            summary['payments'] = f"{len(preserved_data['payment_data'])} records preserved"
        if preserved_data.get('transaction_data'):
            summary['transactions'] = f"{len(preserved_data['transaction_data'])} records preserved"
        if preserved_data.get('security_data'):
            summary['security'] = 'Preserved'

        return summary

    def _clear_existing_preserved_data(self, email):
        """Clear any existing preserved data for this email to respect new deletion preferences"""
        try:
            print(f"🧹 Clearing existing preserved data for email: {email}")

            # Find all existing deletion records for this email
            existing_records = AccountDeletionRecord.where('email', email)\
                .where('deletion_status', 'completed')\
                .get()

            for record in existing_records:
                # CRITICAL FIX: Completely clear preserved data fields
                # Set to None and also clear any JSON data
                record.preserved_user_data = None
                record.preserved_payment_data = None
                record.preserved_transaction_data = None
                record.preserved_security_data = None

                # Also clear the data_restored_at timestamp to reset restoration status
                record.data_restored_at = None

                record.save()
                print(f"✅ Cleared preserved data from deletion record: {record.deletion_id}")

            print(f"🧹 Cleared preserved data from {len(existing_records)} existing records")

        except Exception as e:
            print(f"⚠️ Error clearing existing preserved data: {str(e)}")
            # Don't fail the deletion process if clearing fails
