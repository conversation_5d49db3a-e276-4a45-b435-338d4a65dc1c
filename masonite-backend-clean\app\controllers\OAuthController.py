"""OAuth Controller for handling OAuth authentication flows"""

import os
import json
from datetime import datetime
from urllib.parse import quote
from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.environment import env
from app.models.User import User
from app.models.OAuthAuthorizationCode import OAuthAuthorizationCode
from app.services.OAuthService import OAuthService
from app.controllers.CorsController import CorsController


class OAuthController(Controller):
    """Controller for OAuth authentication endpoints"""

    def __init__(self):
        self.oauth_service = OAuthService()
        # Use CORS_ORIGIN as frontend URL (should be http://localhost:4200)
        self.frontend_url = env('CORS_ORIGIN', 'http://localhost:4200')

    def get_oauth_url(self, request: Request, response: Response):
        """GET /api/oauth/{provider}/url - Get OAuth authorization URL"""
        try:
            provider = request.param('provider')
            
            # Validate provider
            supported_providers = ['google', 'github', 'microsoft']
            if provider not in supported_providers:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': f'Unsupported OAuth provider: {provider}'
                    }
                }, 400)

            # Check if provider is configured
            if not self.oauth_service.is_provider_configured(provider):
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 503,
                        'name': 'ServiceUnavailable',
                        'message': f'{provider} OAuth is not configured. Please set environment variables.'
                    }
                }, 503)

            # Generate state parameter
            state = f"{provider}_{int(datetime.now().timestamp())}_{os.urandom(8).hex()}"
            
            # Generate OAuth URL
            url = self.oauth_service.generate_oauth_url(provider, state)

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'url': url,
                'provider': provider,
                'state': state
            })

        except Exception as e:
            print(f"❌ OAuth URL Generation Error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': f'Failed to generate {provider} OAuth URL'
                }
            }, 500)

    def handle_oauth_callback(self, request: Request, response: Response):
        """POST /api/oauth/{provider}/callback - Handle OAuth callback"""
        try:
            provider = request.param('provider')
            
            # Validate request data
            validator = Validator()
            errors = validator.validate(request.all(), {
                'code': 'required|string'
            })

            if errors:
                error_messages = []
                for field, field_errors in errors.items():
                    error_messages.extend(field_errors)
                
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'ValidationError',
                        'message': 'Validation failed',
                        'details': {'messages': error_messages}
                    }
                }, 400)

            code = request.input('code')
            
            print(f"🔗 Processing OAuth callback for provider: {provider}")

            # Exchange code for access token
            access_token = self.oauth_service.exchange_code_for_token(provider, code)
            
            # Verify token and get user data
            if provider == 'google':
                oauth_user_data = self.oauth_service.verify_google_token(access_token)
            elif provider == 'github':
                oauth_user_data = self.oauth_service.verify_github_token(access_token)
            elif provider == 'microsoft':
                oauth_user_data = self.oauth_service.verify_microsoft_token(access_token)
            else:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Unsupported OAuth provider'
                    }
                }, 400)

            print(f"✅ OAuth user data retrieved: {oauth_user_data['email']}")

            # PERFORMANCE FIX: Combine user lookup and creation in one step
            user = self.oauth_service.find_or_create_oauth_user(provider, oauth_user_data)

            # Determine if this was a new user (check if created_at is very recent)
            from datetime import datetime, timedelta
            is_new_user = user.created_at and user.created_at > (datetime.now() - timedelta(seconds=30))

            # Generate JWT token
            token = user.generate_api_token()

            print(f"✅ OAuth login successful for {'new' if is_new_user else 'existing'} user")

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'token': token,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'firstName': user.first_name,
                    'lastName': user.last_name,
                    'avatarUrl': user.avatar_url,
                    'emailVerified': bool(user.email_verified_at),
                    'roles': user.roles or ['user']
                },
                'isNewUser': is_new_user
            })

        except Exception as e:
            print(f"❌ OAuth Callback Error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'OAuth authentication failed'
                }
            }, 500)

    def handle_oauth_redirect(self, request: Request, response: Response):
        """GET /api/oauth/callback - Handle OAuth redirect from provider"""

        try:
            code = request.input('code')
            state = request.input('state')
            error = request.input('error')
            
            print(f"🔗 OAuth callback received - Code: {bool(code)}, State: {state}, Error: {error}")
            print(f"🔗 Frontend URL configured as: {self.frontend_url}")
            
            if error:
                print(f"❌ OAuth Error: {error}")
                print(f"🔗 Frontend URL is: '{self.frontend_url}'")

                error_url = f"{self.frontend_url}/auth/oauth-error?error={error}"
                print(f"🔗 Redirecting to error URL: {error_url}")

                # FIX: Use manual Location header instead of response.redirect()
                # because response.redirect() was stripping the port number from URLs
                response.header('Location', error_url)
                response.status(302)
                return response

            if not code or not state:
                print("❌ Missing authorization code or state parameter")
                error_url = f"{self.frontend_url}/auth/oauth-error?error={quote('Missing authorization code or state parameter')}"
                print(f"🔗 Redirecting to error URL: {error_url}")
                # FIX: Use manual Location header instead of response.redirect()
                response.header('Location', error_url)
                response.status(302)
                return response

            # Extract provider from state
            provider = state.split('_')[0]
            print(f"🔗 Processing OAuth redirect for provider: {provider}")

            # Exchange code for access token
            access_token = self.oauth_service.exchange_code_for_token(provider, code)
            
            # Verify token and get user data
            if provider == 'google':
                oauth_user_data = self.oauth_service.verify_google_token(access_token)
            elif provider == 'github':
                oauth_user_data = self.oauth_service.verify_github_token(access_token)
            elif provider == 'microsoft':
                oauth_user_data = self.oauth_service.verify_microsoft_token(access_token)
            else:
                print(f"❌ Unsupported OAuth provider: {provider}")
                error_url = f"{self.frontend_url}/auth/oauth-error?error={quote('Unsupported OAuth provider')}"
                print(f"🔗 Redirecting to error URL: {error_url}")
                # FIX: Use manual Location header instead of response.redirect()
                response.header('Location', error_url)
                response.status(302)
                return response

            print(f"✅ OAuth user data retrieved: {oauth_user_data['email']}")

            # PERFORMANCE FIX: Combine user lookup and creation in one step
            user = self.oauth_service.find_or_create_oauth_user(provider, oauth_user_data)

            # Determine if this was a new user (check if created_at is very recent)
            from datetime import datetime, timedelta
            is_new_user = user.created_at and user.created_at > (datetime.now() - timedelta(seconds=30))

            # Generate one-time authorization code
            auth_code_record = OAuthAuthorizationCode.create_code(
                user_id=str(user.id),
                provider=provider,
                is_new_user=is_new_user,
                expires_minutes=5
            )

            print("✅ OAuth authorization code generated - redirecting to frontend")
            
            # Redirect to frontend with one-time authorization code
            success_url = f"{self.frontend_url}/auth/oauth-success?code={auth_code_record.code}&provider={provider}"
            print(f"🔗 Redirecting to success URL: {success_url}")
            # FIX: Use manual Location header instead of response.redirect()
            response.header('Location', success_url)
            response.status(302)
            return response

        except Exception as e:
            print(f"❌ OAuth Redirect Error: {str(e)}")

            # Redirect to frontend error page instead of returning JSON
            error_url = f"{self.frontend_url}/auth/oauth-error?error={quote(str(e))}"
            print(f"🔗 Redirecting to error URL: {error_url}")
            # FIX: Use manual Location header instead of response.redirect()
            response.header('Location', error_url)
            response.status(302)
            return response

    def exchange_authorization_code(self, request: Request, response: Response):
        """POST /api/oauth/exchange-token - Exchange authorization code for JWT token"""
        try:
            print("🔄 Processing authorization code exchange")
            
            # Get request data and handle empty payload
            request_data = request.all()

            # Handle case where request body is empty or not JSON
            if not request_data:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Request body is required'
                    }
                }, 400)
            
            # Check if code field exists (simple validation first)
            if 'code' not in request_data:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Authorization code is required'
                    }
                }, 400)
            
            code = request_data.get('code')
            print(f"🔍 Code received: '{code}' (type: {type(code)})")
            
            # Validate code is not None or empty
            if code is None or (isinstance(code, str) and code.strip() == ""):
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Authorization code cannot be empty'
                    }
                }, 400)

            # Clean up expired codes first
            try:
                OAuthAuthorizationCode.cleanup_expired_codes()
            except Exception as cleanup_error:
                print(f"⚠️ Cleanup error (non-critical): {cleanup_error}")

            # Find and use the authorization code
            try:
                auth_code = OAuthAuthorizationCode.find_and_use_code(code)
            except Exception as code_error:
                print(f"❌ Code lookup error: {code_error}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Invalid or expired authorization code'
                    }
                }, 400)

            if not auth_code:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequest',
                        'message': 'Invalid or expired authorization code'
                    }
                }, 400)

            print(f"✅ Valid authorization code found for user: {auth_code.user_id}")

            # Get user data
            user = User.where('id', auth_code.user_id).first()
            print(f"🔍 Debug - User query result: {type(user)} - {user}")
            
            if not user:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFound',
                        'message': 'User not found'
                    }
                }, 404)

            # Ensure we have a proper User model instance
            if isinstance(user, dict):
                print("⚠️ Warning: User query returned dict, attempting to convert to model")
                # If the query returned a dict, try to get the proper model instance
                user = User.find(auth_code.user_id)
                if not user:
                    CorsController.add_cors_headers(response, request.header('Origin'))
                    return response.json({
                        'error': {
                            'statusCode': 404,
                            'name': 'NotFound',
                            'message': 'User not found'
                        }
                    }, 404)

            # Generate JWT token
            print(f"🔍 Debug - About to call generate_api_token on user type: {type(user)}")
            print(f"🔍 Debug - User has generate_api_token method: {hasattr(user, 'generate_api_token')}")
            
            try:
                token = user.generate_api_token()
            except Exception as token_error:
                print(f"❌ Token generation error: {token_error}")
                print(f"❌ User object: {user}")
                print(f"❌ User type: {type(user)}")
                # Try alternative approach if main method fails
                if hasattr(user, 'api_token'):
                    import secrets
                    token = secrets.token_urlsafe(40)
                    user.api_token = token
                    user.save()
                    print("✅ Used fallback token generation")
                else:
                    raise token_error

            print("✅ JWT token generated successfully for OAuth user")

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'token': token,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'firstName': user.first_name,
                    'lastName': user.last_name,
                    'avatarUrl': user.avatar_url,
                    'emailVerified': bool(user.email_verified_at),
                    'roles': user.roles or ['user']
                },
                'isNewUser': auth_code.is_new_user,
                'provider': auth_code.provider
            })

        except Exception as e:
            print(f"❌ Authorization Code Exchange Error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Token exchange failed'
                }
            }, 500)

    def get_available_providers(self, request: Request, response: Response):
        """GET /api/oauth/providers - Get available OAuth providers"""
        try:
            providers = self.oauth_service.get_available_providers()

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'providers': providers
            })

        except Exception as e:
            print(f"❌ Get Providers Error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get providers'
                }
            }, 500)


